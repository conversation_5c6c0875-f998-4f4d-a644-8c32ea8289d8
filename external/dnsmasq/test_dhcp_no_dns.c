#include "src/dnsmasq.h"
#include <stdio.h>

int main() {
    // Test that our new option constant is defined
    printf("OPT2_NO_DHCP_DNS = %u\n", OPT2_NO_DHCP_DNS);
    
    // Test that we can create a daemon structure with options2
    struct daemon test_daemon;
    test_daemon.options2 = 0;
    test_daemon.options2 |= OPT2_NO_DHCP_DNS;
    
    printf("options2 with OPT2_NO_DHCP_DNS set = %u\n", test_daemon.options2);
    
    if (test_daemon.options2 & OPT2_NO_DHCP_DNS) {
        printf("OPT2_NO_DHCP_DNS flag is correctly set!\n");
    } else {
        printf("ERROR: OPT2_NO_DHCP_DNS flag is not set!\n");
        return 1;
    }
    
    return 0;
}
